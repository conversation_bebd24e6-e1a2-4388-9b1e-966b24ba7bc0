<?php

return [

  /*
  |--------------------------------------------------------------------------
  | Authentication Language Lines
  |--------------------------------------------------------------------------
  |
  | The following language lines are used during authentication for various
  | messages that we need to display to the user. You are free to modify
  | these language lines according to your application's requirements.
  |
   */

  'loggedOut'           => 'Log Out successfully',
  'AdminNotify'         => 'Admin Notify',
  'photoadadded'        => 'Photo Ad Added successfully',
  'passwordReset'       => 'Password Reset successfully',
  'newComment'          => 'New Comment',
  'adDeleted'           => 'Ad Deleted successfully',
  'reportAdded'         => 'Report Added successfully',
  'signed'              => 'sign in successfully',
  'added'               => 'Added successfully',
  'commentAdded'        => 'Comment Added successfully',
  'commentDeleted'      => 'Comment deleted successfully',
  'unauthorize'         => 'un authorize to process this action',
  'rated'               => 'Your Rate Added successfully',
  'fav'                 => 'Added to Favorite successfully',
  'unFav'               => 'Deleted from Favorite successfully',
  'openNotify'          => 'Notifications Open successfully',
  'closeNotify'         => 'Notifications Closed successfully',
  'transfered'          => 'Bank transfer sent successfully',
  'addAdded'            => 'ad Added successfully',
  'newMessage'          => 'You have new message from: attr',
  'messageSended'       => 'Message Sended successfully',
  'updated'             => 'updated successfully',
  'refreshed'           => 'Ad Refreshed successfully',
  'send_activated'      => 'Activation Code Send successfully',
  'phone_changed'       => 'Phone Changed successfully',
  'email_changed'       => 'Email Changed successfully',
  'phone_update_code'   => 'Phone Update Verification Code',
  'verification_code_sent' => 'Verification code sent successfully',
  'phone_updated_successfully' => 'Phone number updated successfully',

  // Service messages
  'service_created_successfully' => 'Service created successfully',
  'service_updated_successfully' => 'Service updated successfully',
  'service_deleted_successfully' => 'Service deleted successfully',
  'service_activated_successfully' => 'Service activated successfully',
  'service_deactivated_successfully' => 'Service deactivated successfully',
  'service_not_found' => 'Service not found',
  'unauthorized_access' => 'Unauthorized access',

  // Working Hours messages
  'working_hours_saved_successfully' => 'Working hours saved successfully',
  'success'             => 'success',
  'deleted'             => 'deleted',
  'not_avilable_coupon' => 'The coupon is not available for use at the moment',
   'coupon_end_at'      => 'coupon expired on  : date',
  'coupon_start_at'     => 'coupon start usage on  : date',
   'disc_amount'        => 'discount amount',
   'rs'                 => 'Saudi Riyals',
   'model_not_found'    => 'This item not found!',
   'route_not_found'    => 'This http url not exists!',
   'activeCode'          => 'your activation code: ',
   'removed'             => 'Removed successfully',
   'category_not_found'  => 'Category not found!',
   'product_not_found'   => 'Product not found!',
   'product_created_successfully' => 'Product created successfully',
   'product_updated_successfully' => 'Product updated successfully',
   'product_deleted_successfully' => 'Product deleted successfully',
   'product_restored_successfully' => 'Product restored successfully',
   'product_status_updated' => 'Product status updated successfully',
   'cart_is_empty'       => 'Cart is empty',
   'cart_empty'          => 'Your cart is empty. Please add products to your cart before creating an order.',
   'cart_not_found'      => 'Cart not found',
   'product_added_to_cart' => 'Product added to cart successfully',
   'cart_updated'        => 'Cart updated successfully',
   'product_removed_from_cart' => 'Product removed from cart successfully',
   'cart_cleared'        => 'Cart cleared successfully',
   'product_not_available' => 'Product is not available',
   'insufficient_stock'  => 'Insufficient stock',
   'order_created'       => 'Order created successfully',
   'order_canceled'      => 'Order canceled successfully',
   'closeOrder'          => 'Order completed successfully',
   'order_cannot_be_canceled' => 'This order cannot be canceled',
   'order_cannot_be_cancelled_at_this_stage' => 'Order cannot be cancelled at this stage',
   'cancellation_request_already_submitted' => 'Cancellation request already submitted',
   'cancellation_request_submitted_successfully' => 'Cancellation request submitted successfully. Admin will review your request.',
   'failed_to_submit_cancellation_request' => 'Failed to submit cancellation request',
   'order_status_updated' => 'Order status updated successfully',
   'invalid_order_status' => 'Invalid order status',
   'cannot_use_cancel_status' => 'Please use the cancel order endpoint to cancel an order',
   'address_not_found'   => 'Address not found',
   'max_usa_coupon'      => 'This coupon has reached its maximum usage limit',
   'order_creation_failed' => 'Failed to create order. Please try again.',
    'order_not_found'     => 'Order not found',
    'insufficient_wallet_balance' => 'Insufficient wallet balance',
    // Order Rating Messages
    'order_id_required' => 'Order ID is required',
    'rate_required' => 'Rating is required',
    'rate_must_be_numeric' => 'Rating must be a number',
    'rate_min_value' => 'Rating must be at least 1',
    'rate_max_value' => 'Rating cannot be more than 5',
    'note_max_length' => 'Note cannot be more than 1000 characters',
    'order_not_belongs_to_user' => 'This order does not belong to you',
    'order_must_be_delivered_to_rate' => 'Order must be delivered before it can be rated',
    'order_already_rated' => 'This order has already been rated',
    'order_not_rated' => 'This order has not been rated yet',
    'order_rated_successfully' => 'Order rated successfully',
    'order_rating_updated_successfully' => 'Order rating updated successfully',

    // Order Report Messages
    'report_note_required' => 'Report note is required',
    'report_note_must_be_string' => 'Report note must be text',
    'report_note_max_length' => 'Report note cannot be more than 1000 characters',
    'order_already_reported' => 'You have already reported this order',
    'report_not_belongs_to_user' => 'This report does not belong to you',
    'order_reported_successfully' => 'Order reported successfully',

    // Rating Messages (Polymorphic)
    'rateable_type_required' => 'Item type is required',
    'invalid_rateable_type' => 'Invalid item type. Allowed types: provider, product, service',
    'rateable_id_required' => 'Item ID is required',
    'rateable_id_must_be_integer' => 'Item ID must be a number',
    'item_not_found' => 'Item not found',
    'item_already_rated' => 'You have already rated this item',
    'item_rated_successfully' => 'Item rated successfully',
    'rating_not_found' => 'Rating not found',
    'rateable_type_and_id_required' => 'Both item type and ID are required',
    'body_max_length' => 'Review text cannot be more than 1000 characters',
    'max_files_allowed' => 'Maximum 5 files are allowed',
    'file_must_be_valid' => 'File must be a valid file',
    'invalid_file_format' => 'Invalid file format. Allowed formats: jpeg, png, jpg, gif, mp4, mov, avi, wmv, flv, webm',
    'file_size_too_large' => 'File size is too large. Maximum allowed size is 10MB',

    // Legacy Provider Rating Messages (for backward compatibility)
    'provider_id_required' => 'Provider ID is required',
    'provider_not_found' => 'Provider not found',
    'provider_already_rated' => 'You have already rated this provider',
    'provider_rated_successfully' => 'Provider rated successfully',
//addresses_shown
  'addresses_shown' => 'Addresses shown successfully',

  'address_created' => 'Address created successfully',
  'address_updated' => 'Address updated successfully',
  'address_deleted' => 'Address deleted successfully',
  //addresses_unauthorized
  'addresses_unauthorized' => 'Unauthorized to access addresses',
  'not_found' => 'Not found',
  'addresses' => [
    'unauthorized' => 'Unauthorized to access this address',
    'not_found' => 'Address not found',
    'shown' => 'Address details shown successfully',
    'updated' => 'Address updated successfully',
    'deleted' => 'Address deleted successfully',
  ],

    // Gifts
    'gifts_retrieved_successfully' => 'Gifts retrieved successfully',
    'available_gifts_retrieved_successfully' => 'Available gifts retrieved successfully',
    'pending_gifts_retrieved_successfully' => 'Pending gifts retrieved successfully',
    'stats_retrieved_successfully' => 'Statistics retrieved successfully',
    'no_gifts_available' => 'No gifts available for this month',
    'unauthorized' => 'Unauthorized access',
    'something_went_wrong' => 'Something went wrong',
    'currency' => 'SAR',

  // Account deletion requests
  'deletion_request_already_exists' => 'A pending account deletion request already exists',
  'deletion_request_submitted' => 'Account deletion request submitted successfully',
  'reason_must_be_string' => 'Reason must be a string',
  'reason_too_long' => 'Reason is too long',
  'cart_services_cleared' => 'All services have been removed from the cart successfully',
   'service_different_provider_with_products' => 'Adding this service will remove all current products from your cart as they are from a different provider. Do you want to continue?',
   'product_different_provider_with_services' => 'Adding this product will remove all services from your cart as they are from a different provider. Do you want to continue?',
  'male' => 'male',
  'female' => 'female',

   'bank_info_required'     => 'Please complete your bank account information before requesting a withdrawal.',
    'insufficient_balance'   => 'Insufficient wallet balance.',
    'withdraw_notice'        => 'Your bank account will be verified before confirming the request. The transfer process may take up to 72 hours.',
    'coupon_applied'=> 'Coupon applied successfully.',
  'status_already_set' => 'Order already has this status',
  'rejected' => 'Your account has been rejected by the admin',
  'orders_const' => 'you should at leaset add one product or service to accept orders',
  'work_hours' => 'you should add working hours to accept orders'

  ];
